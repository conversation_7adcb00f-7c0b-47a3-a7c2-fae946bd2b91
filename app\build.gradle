import com.android.build.gradle.api.ApplicationVariant
import dependencies.Versions

apply plugin: 'com.android.application'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'com.google.android.gms.oss-licenses-plugin'

android {
    compileSdk 34
    defaultConfig {
        applicationId "org.mozilla.screenshot.go"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "0.8"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        def build_number = System.getenv("BITRISE_BUILD_NUMBER")
        if (build_number?.trim()) {
            def build_number_with_offset = build_number.toInteger()
            versionCode build_number_with_offset
            versionNameSuffix "(" + build_number_with_offset + ")"
        }

        compileOptions {
            sourceCompatibility JavaVersion.VERSION_11
            targetCompatibility JavaVersion.VERSION_11
        }

        kotlinOptions {
            jvmTarget = "11"
        }

        javaCompileOptions {
            annotationProcessorOptions {
                arguments = ["room.schemaLocation": "$projectDir/schemas".toString()]
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        debug {
            applicationIdSuffix ".debug"
            versionNameSuffix applicationIdSuffix
        }
    }

    flavorDimensions "product"

    productFlavors {
        go {
            resConfigs "en", "in"
            dimension "product"
        }

        preview {
            applicationId "gro.allizom.scryer"
            applicationIdSuffix ""
            versionNameSuffix ".nightly"
            resConfigs "en", "in"
            dimension "product"
        }
    }

    variantFilter { variant ->
        def flavors = variant.flavors*.name
        // We only need a gecko debug and beta build for now.
        if (flavors.contains("preview") && variant.buildType.name != "release") {
            setIgnore(true)
        }
    }

    applicationVariants.all { variant ->
        buildConfigField("String", "ADJUST_TOKEN", "\"${getAdjustToken()}\"")
        buildConfigField("String", "ADJUST_ENVIRONMENT", getAdjustEnvironment(variant))
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    // Firebase BOM for version management
    implementation platform("com.google.firebase:firebase-bom:${Versions.firebase}")

    // Support
    implementation "androidx.appcompat:appcompat:${Versions.app_compat}"
    implementation "com.google.android.material:material:${Versions.material}"
    implementation "androidx.cardview:cardview:${Versions.support}"
    implementation "androidx.recyclerview:recyclerview:${Versions.recycler_view}"
    implementation "androidx.transition:transition:${Versions.transision}"
    implementation "androidx.preference:preference:1.2.1"
    implementation "androidx.media:media:${Versions.media}"
    implementation "androidx.legacy:legacy-support-v4:${Versions.support}"
    implementation "androidx.constraintlayout:constraintlayout:${Versions.constraint_layout}"

    // Ktx
    implementation "androidx.core:core-ktx:${Versions.ktx}"

    // Lifecycle
    implementation "androidx.lifecycle:lifecycle-common-java8:${Versions.lifecycle}"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:${Versions.lifecycle}"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:${Versions.lifecycle}"

    // Navigation
    implementation "androidx.navigation:navigation-ui:${Versions.navigation}"
    implementation "androidx.navigation:navigation-fragment:${Versions.navigation}"

    // Room
    implementation "androidx.room:room-runtime:${Versions.room}"
    implementation "androidx.room:room-ktx:${Versions.room}"
    kapt "androidx.room:room-compiler:${Versions.room}"

    // Work
    implementation "androidx.work:work-runtime:${Versions.work}"

    // Kotlin
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:${Versions.kotlin}"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:${Versions.kotlin_coroutine}"

    // Glide
    implementation "com.github.bumptech.glide:glide:${Versions.glide}"

    // Firebase (using BOM for version management)
    implementation "com.google.firebase:firebase-analytics"
    implementation "com.google.firebase:firebase-messaging"
    implementation "com.google.firebase:firebase-crashlytics:${Versions.firebase_crashlytics}"

    // ML Kit (replaces Firebase ML Vision)
    implementation "com.google.mlkit:text-recognition:${Versions.firebase_ml}"

    // Adjust
    implementation "com.adjust.sdk:adjust-android:${Versions.adjust}"

    implementation "com.android.installreferrer:installreferrer:${Versions.android_install_referrer}"
    implementation "com.google.android.gms:play-services-analytics:17.0.1" // Required by Adjust

    // Mozilla
    implementation "org.mozilla.components:browser-search:${Versions.android_components}"
    implementation "org.mozilla.components:service-telemetry:${Versions.android_components}"
    implementation project(':telemetry-annotation')
    kapt project(':telemetry-compiler')

    // SubsamplingScaleImageView
    implementation "com.davemorrissey.labs:subsampling-scale-image-view:${Versions.subsampling_image_view}"

    // Lottie
    implementation "com.airbnb.android:lottie:${Versions.lottie}"

    // License
    implementation "com.google.android.gms:play-services-oss-licenses:${Versions.license}"

    // BetterLinkMovementMethod
    implementation "me.saket:better-link-movement-method:${Versions.better_link_movement_method}"

    // junit
    testImplementation "junit:junit:${Versions.junit}"

    // ATSL
    androidTestImplementation "androidx.test:runner:${Versions.test_runner}"

    // Espresso
    androidTestImplementation "androidx.test.espresso:espresso-core:${Versions.espresso}"

    // Mockito
    testImplementation "org.mockito:mockito-core:${Versions.mockito}"
}
repositories {
    mavenCentral()
}



static def getAdjustToken() {
    return System.getenv("ADJUST_TOKEN") ?: ""
}

static def getAdjustEnvironment(ApplicationVariant variant) {
    return (variant.buildType.name == "release") ?
            "com.adjust.sdk.AdjustConfig.ENVIRONMENT_PRODUCTION" :
            "com.adjust.sdk.AdjustConfig.ENVIRONMENT_SANDBOX"
}

apply plugin: 'com.google.gms.google-services'
