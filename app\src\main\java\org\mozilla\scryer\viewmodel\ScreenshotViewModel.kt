/* -*- Mode: Java; c-basic-offset: 4; tab-width: 4; indent-tabs-mode: nil; -*-
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */

package org.mozilla.scryer.viewmodel

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelProviders
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.mozilla.scryer.ScryerApplication
import org.mozilla.scryer.persistence.ScreenshotModel
import org.mozilla.scryer.repository.ScreenshotRepository

class ScreenshotViewModel(private val delegate: ScreenshotRepository) : ViewModel(),
        ScreenshotRepository by delegate {
    companion object {
        fun get(fragment: Fragment): ScreenshotViewModel {
            return ViewModelProvider(fragment, getFactory())[ScreenshotViewModel::class.java]
        }

        fun get(activity: FragmentActivity): ScreenshotViewModel {
            return ViewModelProvider(activity, getFactory())[ScreenshotViewModel::class.java]
        }

        private fun getFactory(): ScreenshotViewModelFactory {
            return ScreenshotViewModelFactory(ScryerApplication.getScreenshotRepository())
        }
    }

    suspend fun batchMove(
            screenshots: List<ScreenshotModel>,
            collectionId: String
    ) = withContext(Dispatchers.Default) {
        screenshots.forEach {
            it.collectionId = collectionId
            updateScreenshots(listOf(it))
        }
    }
}

class ScreenshotViewModelFactory(private val repository: ScreenshotRepository)
    : ViewModelProvider.NewInstanceFactory() {
    override fun <T : ViewModel?> create(modelClass: Class<T>): T {
        @Suppress("UNCHECKED_CAST")
        return ScreenshotViewModel(repository) as T
    }
}